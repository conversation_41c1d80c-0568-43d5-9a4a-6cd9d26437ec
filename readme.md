# API sqeuence diagram
```mermaid    
sequenceDiagram
    participant C as Client
    participant S as APIServer
    participant D as DB


    note over C,D :仕事の登録
    C ->>+ S : POST /setjob(DriverId,TIME)
    note over S : validation
    S ->> D: SELECT
    D -->> S : Responce(WorkID)


    alt WorkIDが何かしら返ってきたら

    S -->> C : Response(Suc)  

    else WorkIDが何も返ってこなかったら

    S ->>+ D : INSERT
    S -->>- C : Response(Suc)
    end




    note over C,D :ドライバーの取得
    C ->>+ S : GET /findDriver(TIME)
    S ->>+ D : SELECT
    D -->>- S : Response(DriverID,DriverName)
    S -->>- C : Response(DriverID,DriverName) 
    note over C,D :仕事の取得

    

```

# DB difinition

## Driver Table
|--|DriverID|DriverName|valid|
|--|--|--|--|
|Type|INT|TEXT|BOOL|
|PK|○|--|--|
|UQ|○|--|--|
|DEFAULT|--|--|true|