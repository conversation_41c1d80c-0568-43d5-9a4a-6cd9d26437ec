/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/appsettings.Development.json
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/appsettings.json
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/SampleAPI
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/SampleAPI.deps.json
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/SampleAPI.runtimeconfig.json
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/SampleAPI.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/SampleAPI.pdb
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/Microsoft.AspNetCore.OpenApi.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/Microsoft.OpenApi.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/Swashbuckle.AspNetCore.Swagger.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/Swashbuckle.AspNetCore.SwaggerGen.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/Swashbuckle.AspNetCore.SwaggerUI.dll
/home/<USER>/git_file/CS/SampleAPI/obj/Debug/net8.0/SampleAPI.csproj.AssemblyReference.cache
/home/<USER>/git_file/CS/SampleAPI/obj/Debug/net8.0/SampleAPI.GeneratedMSBuildEditorConfig.editorconfig
/home/<USER>/git_file/CS/SampleAPI/obj/Debug/net8.0/SampleAPI.AssemblyInfoInputs.cache
/home/<USER>/git_file/CS/SampleAPI/obj/Debug/net8.0/SampleAPI.AssemblyInfo.cs
/home/<USER>/git_file/CS/SampleAPI/obj/Debug/net8.0/SampleAPI.csproj.CoreCompileInputs.cache
/home/<USER>/git_file/CS/SampleAPI/obj/Debug/net8.0/SampleAPI.MvcApplicationPartsAssemblyInfo.cs
/home/<USER>/git_file/CS/SampleAPI/obj/Debug/net8.0/SampleAPI.MvcApplicationPartsAssemblyInfo.cache
/home/<USER>/git_file/CS/SampleAPI/obj/Debug/net8.0/staticwebassets.build.json
/home/<USER>/git_file/CS/SampleAPI/obj/Debug/net8.0/staticwebassets.development.json
/home/<USER>/git_file/CS/SampleAPI/obj/Debug/net8.0/staticwebassets/msbuild.SampleAPI.Microsoft.AspNetCore.StaticWebAssets.props
/home/<USER>/git_file/CS/SampleAPI/obj/Debug/net8.0/staticwebassets/msbuild.build.SampleAPI.props
/home/<USER>/git_file/CS/SampleAPI/obj/Debug/net8.0/staticwebassets/msbuild.buildMultiTargeting.SampleAPI.props
/home/<USER>/git_file/CS/SampleAPI/obj/Debug/net8.0/staticwebassets/msbuild.buildTransitive.SampleAPI.props
/home/<USER>/git_file/CS/SampleAPI/obj/Debug/net8.0/staticwebassets.pack.json
/home/<USER>/git_file/CS/SampleAPI/obj/Debug/net8.0/scopedcss/bundle/SampleAPI.styles.css
/home/<USER>/git_file/CS/SampleAPI/obj/Debug/net8.0/SampleAPI.csproj.CopyComplete
/home/<USER>/git_file/CS/SampleAPI/obj/Debug/net8.0/SampleAPI.dll
/home/<USER>/git_file/CS/SampleAPI/obj/Debug/net8.0/refint/SampleAPI.dll
/home/<USER>/git_file/CS/SampleAPI/obj/Debug/net8.0/SampleAPI.pdb
/home/<USER>/git_file/CS/SampleAPI/obj/Debug/net8.0/SampleAPI.genruntimeconfig.cache
/home/<USER>/git_file/CS/SampleAPI/obj/Debug/net8.0/ref/SampleAPI.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/Azure.Core.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/Azure.Identity.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/Humanizer.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/Microsoft.Bcl.AsyncInterfaces.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/Microsoft.Build.Locator.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/Microsoft.CodeAnalysis.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/Microsoft.CodeAnalysis.CSharp.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/Microsoft.CodeAnalysis.CSharp.Workspaces.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/Microsoft.CodeAnalysis.Workspaces.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/Microsoft.Data.SqlClient.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/Microsoft.EntityFrameworkCore.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/Microsoft.EntityFrameworkCore.Design.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/Microsoft.EntityFrameworkCore.Relational.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/Microsoft.EntityFrameworkCore.SqlServer.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/Microsoft.Extensions.Caching.Abstractions.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/Microsoft.Extensions.Caching.Memory.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/Microsoft.Extensions.DependencyInjection.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/Microsoft.Extensions.DependencyModel.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/Microsoft.Extensions.Logging.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/Microsoft.Extensions.Logging.Abstractions.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/Microsoft.Extensions.Options.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/Microsoft.Extensions.Primitives.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/Microsoft.Identity.Client.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/Microsoft.Identity.Client.Extensions.Msal.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/Microsoft.IdentityModel.Abstractions.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/Microsoft.IdentityModel.Logging.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/Microsoft.IdentityModel.Protocols.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/Microsoft.IdentityModel.Tokens.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/Microsoft.SqlServer.Server.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/Microsoft.Win32.SystemEvents.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/Mono.TextTemplating.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/System.ClientModel.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/System.CodeDom.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/System.Composition.AttributedModel.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/System.Composition.Convention.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/System.Composition.Hosting.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/System.Composition.Runtime.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/System.Composition.TypedParts.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/System.Configuration.ConfigurationManager.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/System.Diagnostics.DiagnosticSource.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/System.Drawing.Common.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/System.Formats.Asn1.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/System.IdentityModel.Tokens.Jwt.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/System.IO.Pipelines.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/System.Memory.Data.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/System.Runtime.Caching.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/System.Security.Cryptography.ProtectedData.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/System.Security.Permissions.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/System.Text.Encodings.Web.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/System.Text.Json.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/System.Windows.Extensions.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/cs/Microsoft.CodeAnalysis.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/de/Microsoft.CodeAnalysis.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/es/Microsoft.CodeAnalysis.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/fr/Microsoft.CodeAnalysis.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/it/Microsoft.CodeAnalysis.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/ja/Microsoft.CodeAnalysis.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/ko/Microsoft.CodeAnalysis.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/pl/Microsoft.CodeAnalysis.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/pt-BR/Microsoft.CodeAnalysis.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/ru/Microsoft.CodeAnalysis.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/tr/Microsoft.CodeAnalysis.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/zh-Hans/Microsoft.CodeAnalysis.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/zh-Hant/Microsoft.CodeAnalysis.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/cs/Microsoft.CodeAnalysis.CSharp.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/de/Microsoft.CodeAnalysis.CSharp.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/es/Microsoft.CodeAnalysis.CSharp.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/fr/Microsoft.CodeAnalysis.CSharp.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/it/Microsoft.CodeAnalysis.CSharp.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/ja/Microsoft.CodeAnalysis.CSharp.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/ko/Microsoft.CodeAnalysis.CSharp.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/pl/Microsoft.CodeAnalysis.CSharp.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/ru/Microsoft.CodeAnalysis.CSharp.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/tr/Microsoft.CodeAnalysis.CSharp.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/de/Microsoft.CodeAnalysis.Workspaces.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/es/Microsoft.CodeAnalysis.Workspaces.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/it/Microsoft.CodeAnalysis.Workspaces.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/zh-Hans/Microsoft.CodeAnalysis.Workspaces.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/cs/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/de/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/es/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/fr/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/it/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/ja/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/ko/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/pl/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/ru/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/tr/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/zh-Hans/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/runtimes/unix/lib/net6.0/Microsoft.Data.SqlClient.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/runtimes/win/lib/net6.0/Microsoft.Data.SqlClient.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/runtimes/win-arm/native/Microsoft.Data.SqlClient.SNI.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/runtimes/unix/lib/net6.0/System.Drawing.Common.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/runtimes/win/lib/net6.0/System.Drawing.Common.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/runtimes/win/lib/net6.0/System.Runtime.Caching.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/runtimes/win/lib/net6.0/System.Security.Cryptography.ProtectedData.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/runtimes/win/lib/net6.0/System.Windows.Extensions.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/Microsoft.Data.Sqlite.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/Microsoft.EntityFrameworkCore.Sqlite.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/SQLitePCLRaw.batteries_v2.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/SQLitePCLRaw.core.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/SQLitePCLRaw.provider.e_sqlite3.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/runtimes/browser-wasm/nativeassets/net8.0/e_sqlite3.a
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/runtimes/linux-arm/native/libe_sqlite3.so
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/runtimes/linux-arm64/native/libe_sqlite3.so
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/runtimes/linux-armel/native/libe_sqlite3.so
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/runtimes/linux-mips64/native/libe_sqlite3.so
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/runtimes/linux-musl-arm/native/libe_sqlite3.so
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/runtimes/linux-musl-arm64/native/libe_sqlite3.so
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/runtimes/linux-musl-s390x/native/libe_sqlite3.so
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/runtimes/linux-musl-x64/native/libe_sqlite3.so
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/runtimes/linux-ppc64le/native/libe_sqlite3.so
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/runtimes/linux-s390x/native/libe_sqlite3.so
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/runtimes/linux-x64/native/libe_sqlite3.so
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/runtimes/linux-x86/native/libe_sqlite3.so
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/runtimes/maccatalyst-arm64/native/libe_sqlite3.dylib
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/runtimes/maccatalyst-x64/native/libe_sqlite3.dylib
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/runtimes/osx-arm64/native/libe_sqlite3.dylib
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/runtimes/osx-x64/native/libe_sqlite3.dylib
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/runtimes/win-arm/native/e_sqlite3.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/runtimes/win-arm64/native/e_sqlite3.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/runtimes/win-x64/native/e_sqlite3.dll
/home/<USER>/git_file/CS/SampleAPI/bin/Debug/net8.0/runtimes/win-x86/native/e_sqlite3.dll
