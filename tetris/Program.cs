using System;

class Tetris
{

}

class Draw
{
    public int[][] stage = new int[22][];

    Draw()
    {
        initStage();
    }

    private void initStage()
    {
        for (int i = 0; i < 22; i++)
        {
            int[] stageStock = new int[12];
            for (int j = 0; j < 12; j++)
            {
                //I tried outside field making but i feel so sleepy so I gave up making a this function ~~R) 
                if (i == 0) 
            }
        }
    }

}


class Program
{
    static void Main()
    {
        Draw draw = new Draw();

        Console.WriteLine(draw.stage);
    }
}