using System;
using Microsoft.EntityFrameworkCore;
using SampleAPI.Entities;

namespace SampleAPI.DbContexts
{
    public class DriverDbContext : DbContext
    {
        public DriverDbContext(DbContextOptions<DriverDbContext> options) : base(options) { }
        public DbSet<Driver> Driver{ get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Driver>()
                .Property(d => d.valid)
                .HasDefaultValue(false);
        }
    }
}